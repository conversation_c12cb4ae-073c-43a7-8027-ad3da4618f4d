# Copyright (c) 2025, Your Company and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, now
import erpnext

salary_slip = frappe.qb.DocType("Salary Slip")
salary_detail = frappe.qb.DocType("Salary Detail")
salary_component = frappe.qb.DocType("Salary Component")


def execute(filters=None):
    if not filters:
        filters = {}

    currency = None
    if filters.get("currency"):
        currency = filters.get("currency")
    company_currency = erpnext.get_company_currency(filters.get("company"))

    salary_slips = get_salary_slips(filters, company_currency)
    if not salary_slips:
        return [], []

    earning_types, ded_types = get_earning_and_deduction_types(salary_slips)
    columns = get_columns(earning_types, ded_types)

    ss_earning_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "earnings"
    )
    ss_ded_map = get_salary_slip_details(
        salary_slips, currency, company_currency, "deductions"
    )

    doj_map = get_employee_doj_map()

    data = []
    for ss in salary_slips:
        row = {
            "salary_slip_id": ss.name,
            "employee": ss.employee,
            "employee_name": ss.employee_name,
            "data_of_joining": doj_map.get(ss.employee),
            "branch": ss.branch,
            "department": ss.department,
            "designation": ss.designation,
            "company": ss.company,
            "start_date": ss.start_date,
            "end_date": ss.end_date,
            "leave_without_pay": ss.leave_without_pay,
            "absent_days": ss.absent_days,
            "payment_days": ss.payment_days,
            "currency": currency or company_currency,
            "total_loan_repayment": ss.total_loan_repayment,
            "docstatus": ss.docstatus,
            "status": get_status_text(ss.docstatus),
        }

        update_column_width(ss, columns)

        for e in earning_types:
            row.update({frappe.scrub(e): ss_earning_map.get(ss.name, {}).get(e)})

        for d in ded_types:
            row.update({frappe.scrub(d): ss_ded_map.get(ss.name, {}).get(d)})

        if currency == company_currency:
            row.update(
                {
                    "gross_pay": flt(ss.gross_pay) * flt(ss.exchange_rate),
                    "total_deduction": flt(ss.total_deduction) * flt(ss.exchange_rate),
                    "net_pay": flt(ss.net_pay) * flt(ss.exchange_rate),
                }
            )

        else:
            row.update(
                {
                    "gross_pay": ss.gross_pay,
                    "total_deduction": ss.total_deduction,
                    "net_pay": ss.net_pay,
                }
            )

        data.append(row)

    return columns, data


def get_status_text(docstatus):
    """Convert docstatus to readable text"""
    status_map = {0: "Draft", 1: "Submitted", 2: "Cancelled"}
    return status_map.get(docstatus, "Unknown")


def get_earning_and_deduction_types(salary_slips):
    salary_component_and_type = {_("Earning"): [], _("Deduction"): []}

    for salary_component in get_salary_components(salary_slips):
        component_type = get_salary_component_type(salary_component)
        salary_component_and_type[_(component_type)].append(salary_component)

    return sorted(salary_component_and_type[_("Earning")]), sorted(
        salary_component_and_type[_("Deduction")]
    )


def update_column_width(ss, columns):
    if ss.branch is not None:
        columns[3].update({"width": 120})
    if ss.department is not None:
        columns[4].update({"width": 120})
    if ss.designation is not None:
        columns[5].update({"width": 120})
    if ss.leave_without_pay is not None:
        columns[9].update({"width": 120})


def get_columns(earning_types, ded_types):
    columns = [
        {
            "label": _("Salary Slip ID"),
            "fieldname": "salary_slip_id",
            "fieldtype": "Link",
            "options": "Salary Slip",
            "width": 150,
        },
        {
            "label": _("Employee"),
            "fieldname": "employee",
            "fieldtype": "Link",
            "options": "Employee",
            "width": 120,
        },
        {
            "label": _("Employee Name"),
            "fieldname": "employee_name",
            "fieldtype": "Data",
            "width": 140,
        },
        {
            "label": _("Status"),
            "fieldname": "status",
            "fieldtype": "Data",
            "width": 100,
        },
        {
            "label": _("Date of Joining"),
            "fieldname": "data_of_joining",
            "fieldtype": "Date",
            "width": 80,
        },
        {
            "label": _("Branch"),
            "fieldname": "branch",
            "fieldtype": "Link",
            "options": "Branch",
            "width": -1,
        },
        {
            "label": _("Department"),
            "fieldname": "department",
            "fieldtype": "Link",
            "options": "Department",
            "width": -1,
        },
        {
            "label": _("Designation"),
            "fieldname": "designation",
            "fieldtype": "Link",
            "options": "Designation",
            "width": 120,
        },
        {
            "label": _("Company"),
            "fieldname": "company",
            "fieldtype": "Link",
            "options": "Company",
            "width": 120,
        },
        {
            "label": _("Start Date"),
            "fieldname": "start_date",
            "fieldtype": "Data",
            "width": 80,
        },
        {
            "label": _("End Date"),
            "fieldname": "end_date",
            "fieldtype": "Data",
            "width": 80,
        },
        {
            "label": _("Leave Without Pay"),
            "fieldname": "leave_without_pay",
            "fieldtype": "Float",
            "width": 50,
        },
        {
            "label": _("Absent Days"),
            "fieldname": "absent_days",
            "fieldtype": "Float",
            "width": 50,
        },
        {
            "label": _("Payment Days"),
            "fieldname": "payment_days",
            "fieldtype": "Float",
            "width": 120,
        },
    ]

    for earning in earning_types:
        columns.append(
            {
                "label": earning,
                "fieldname": frappe.scrub(earning),
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    columns.append(
        {
            "label": _("Gross Pay"),
            "fieldname": "gross_pay",
            "fieldtype": "Currency",
            "options": "currency",
            "width": 120,
        }
    )

    for deduction in ded_types:
        columns.append(
            {
                "label": deduction,
                "fieldname": frappe.scrub(deduction),
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            }
        )

    columns.extend(
        [
            {
                "label": _("Loan Repayment"),
                "fieldname": "total_loan_repayment",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Total Deduction"),
                "fieldname": "total_deduction",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Net Pay"),
                "fieldname": "net_pay",
                "fieldtype": "Currency",
                "options": "currency",
                "width": 120,
            },
            {
                "label": _("Currency"),
                "fieldtype": "Data",
                "fieldname": "currency",
                "options": "Currency",
                "hidden": 1,
            },
        ]
    )
    return columns


def get_salary_components(salary_slips):
    return (
        frappe.qb.from_(salary_detail)
        .where(
            (salary_detail.amount != 0)
            & (salary_detail.parent.isin([d.name for d in salary_slips]))
        )
        .select(salary_detail.salary_component)
        .distinct()
    ).run(pluck=True)


def get_salary_component_type(salary_component):
    return frappe.db.get_value("Salary Component", salary_component, "type", cache=True)


def get_salary_slips(filters, company_currency):
    doc_status = {"Draft": 0, "Submitted": 1, "Cancelled": 2}

    query = frappe.qb.from_(salary_slip).select(salary_slip.star)

    if filters.get("docstatus"):
        query = query.where(
            salary_slip.docstatus == doc_status[filters.get("docstatus")]
        )

    if filters.get("from_date"):
        query = query.where(salary_slip.start_date >= filters.get("from_date"))

    if filters.get("to_date"):
        query = query.where(salary_slip.end_date <= filters.get("to_date"))

    if filters.get("company"):
        query = query.where(salary_slip.company == filters.get("company"))

    if filters.get("employee"):
        query = query.where(salary_slip.employee == filters.get("employee"))

    if filters.get("currency") and filters.get("currency") != company_currency:
        query = query.where(salary_slip.currency == filters.get("currency"))

    if filters.get("department"):
        query = query.where(salary_slip.department == filters["department"])

    if filters.get("designation"):
        query = query.where(salary_slip.designation == filters["designation"])

    if filters.get("branch"):
        query = query.where(salary_slip.branch == filters["branch"])

    salary_slips = query.run(as_dict=1)

    return salary_slips or []


def get_employee_doj_map():
    employee = frappe.qb.DocType("Employee")

    result = (
        frappe.qb.from_(employee).select(employee.name, employee.date_of_joining)
    ).run()

    return frappe._dict(result)


def get_salary_slip_details(salary_slips, currency, company_currency, component_type):
    salary_slips = [ss.name for ss in salary_slips]

    result = (
        frappe.qb.from_(salary_slip)
        .join(salary_detail)
        .on(salary_slip.name == salary_detail.parent)
        .where(
            (salary_detail.parent.isin(salary_slips))
            & (salary_detail.parentfield == component_type)
        )
        .select(
            salary_detail.parent,
            salary_detail.salary_component,
            salary_detail.amount,
            salary_slip.exchange_rate,
        )
    ).run(as_dict=1)

    ss_map = {}

    for d in result:
        ss_map.setdefault(d.parent, frappe._dict()).setdefault(d.salary_component, 0.0)
        if currency == company_currency:
            ss_map[d.parent][d.salary_component] += flt(d.amount) * flt(
                d.exchange_rate if d.exchange_rate else 1
            )
        else:
            ss_map[d.parent][d.salary_component] += flt(d.amount)

    return ss_map


@frappe.whitelist()
def process_salary_slip_approval(salary_slip_ids, action, reason=None):
    """Process salary slip approval/rejection"""
    if not salary_slip_ids:
        return {"status": "error", "message": _("No salary slips selected")}

    if isinstance(salary_slip_ids, str):
        import json

        salary_slip_ids = json.loads(salary_slip_ids)

    success_count = 0
    error_count = 0
    errors = []

    for salary_slip_id in salary_slip_ids:
        try:
            salary_slip = frappe.get_doc("Salary Slip", salary_slip_id)

            # Check permissions
            if not frappe.has_permission("Salary Slip", "write", salary_slip):
                errors.append(f"No permission to modify {salary_slip_id}")
                error_count += 1
                continue

            if action == "approve":
                if salary_slip.docstatus == 0:  # Draft
                    salary_slip.submit()
                    success_count += 1

                    # Add comment
                    salary_slip.add_comment(
                        "Workflow", f"Approved by {frappe.session.user}"
                    )
                else:
                    errors.append(f"{salary_slip_id} is not in draft status")
                    error_count += 1

            elif action == "reject":
                if salary_slip.docstatus == 0:  # Draft
                    # Add rejection comment
                    comment_text = f"Rejected by {frappe.session.user}"
                    if reason:
                        comment_text += f". Reason: {reason}"
                    salary_slip.add_comment("Workflow", comment_text)

                    # You might want to set a custom field for rejection status
                    # or handle rejection differently based on your requirements
                    success_count += 1
                else:
                    errors.append(f"{salary_slip_id} is not in draft status")
                    error_count += 1

        except Exception as e:
            errors.append(f"Error processing {salary_slip_id}: {str(e)}")
            error_count += 1

    # Prepare response message
    message_parts = []
    if success_count > 0:
        action_text = "approved" if action == "approve" else "rejected"
        message_parts.append(
            f"{success_count} salary slip(s) {action_text} successfully"
        )

    if error_count > 0:
        message_parts.append(f"{error_count} salary slip(s) failed")
        if errors:
            message_parts.append(
                "Errors: " + "; ".join(errors[:5])
            )  # Show first 5 errors

    return " | ".join(message_parts)
