// Copyright (c) 2025, Your Company and contributors
// For license information, please see license.txt

frappe.query_reports["Salary Register with Approval"] = {
	filters: [
		{
			fieldname: "from_date",
			label: __("From"),
			fieldtype: "Date",
			default: frappe.datetime.add_months(frappe.datetime.get_today(), -1),
			reqd: 1,
			width: "100px",
		},
		{
			fieldname: "to_date",
			label: __("To"),
			fieldtype: "Date",
			default: frappe.datetime.get_today(),
			reqd: 1,
			width: "100px",
		},
		{
			fieldname: "currency",
			fieldtype: "Link",
			options: "Currency",
			label: __("Currency"),
			default: erpnext.get_currency(frappe.defaults.get_default("Company")),
			width: "50px",
		},
		{
			fieldname: "employee",
			label: __("Employee"),
			fieldtype: "Link",
			options: "Employee",
			width: "100px",
		},
		{
			fieldname: "company",
			label: __("Company"),
			fieldtype: "Link",
			options: "Company",
			default: frappe.defaults.get_user_default("Company"),
			width: "100px",
			reqd: 1,
		},
		{
			fieldname: "docstatus",
			label: __("Document Status"),
			fieldtype: "Select",
			options: ["Draft", "Submitted", "Cancelled"],
			default: "Draft",
			width: "100px",
		},
		{
			fieldname: "department",
			label: __("Department"),
			fieldtype: "Link",
			options: "Department",
			width: "100px",
			get_query: function () {
				return {
					filters: {
						company: frappe.query_report.get_filter_value("company"),
					},
				};
			},
		},
		{
			fieldname: "designation",
			label: __("Designation"),
			fieldtype: "Link",
			options: "Designation",
			width: "100px",
		},
		{
			fieldname: "branch",
			label: __("Branch"),
			fieldtype: "Link",
			options: "Branch",
			width: "100px",
		},
	],

	onload: function(report) {
		// Add custom buttons
		report.page.add_inner_button(__("Approve Selected"), function() {
			approve_selected_salary_slips(report);
		});

		report.page.add_inner_button(__("Approve All"), function() {
			approve_all_salary_slips(report);
		});

		report.page.add_inner_button(__("Reject Selected"), function() {
			reject_selected_salary_slips(report);
		});
	},

	get_datatable_options(options) {
		return Object.assign(options, {
			checkboxColumn: true,
			events: {
				onCheckRow: function(data) {
					// Handle row selection
				}
			}
		});
	}
};

function approve_selected_salary_slips(report) {
	let selected_rows = report.datatable.rowmanager.getCheckedRows();
	if (selected_rows.length === 0) {
		frappe.msgprint(__("Please select salary slips to approve"));
		return;
	}

	let salary_slip_ids = selected_rows.map(row => 
		report.data[row].salary_slip_id
	);

	frappe.confirm(
		__("Are you sure you want to approve {0} salary slip(s)?", [salary_slip_ids.length]),
		function() {
			process_salary_slips(salary_slip_ids, "approve", report);
		}
	);
}

function approve_all_salary_slips(report) {
	if (!report.data || report.data.length === 0) {
		frappe.msgprint(__("No salary slips to approve"));
		return;
	}

	let salary_slip_ids = report.data
		.filter(row => row.docstatus === 0) // Only draft salary slips
		.map(row => row.salary_slip_id);

	if (salary_slip_ids.length === 0) {
		frappe.msgprint(__("No draft salary slips found to approve"));
		return;
	}

	frappe.confirm(
		__("Are you sure you want to approve all {0} draft salary slip(s)?", [salary_slip_ids.length]),
		function() {
			process_salary_slips(salary_slip_ids, "approve", report);
		}
	);
}

function reject_selected_salary_slips(report) {
	let selected_rows = report.datatable.rowmanager.getCheckedRows();
	if (selected_rows.length === 0) {
		frappe.msgprint(__("Please select salary slips to reject"));
		return;
	}

	let salary_slip_ids = selected_rows.map(row => 
		report.data[row].salary_slip_id
	);

	frappe.prompt([
		{
			fieldname: 'reason',
			fieldtype: 'Small Text',
			label: __('Reason for Rejection'),
			reqd: 1
		}
	], function(values) {
		process_salary_slips(salary_slip_ids, "reject", report, values.reason);
	}, __("Reject Salary Slips"));
}

function process_salary_slips(salary_slip_ids, action, report, reason = null) {
	frappe.call({
		method: "custom_reports.custom_reports.custom_reports.report.salary_register_with_approval.salary_register_with_approval.process_salary_slip_approval",
		args: {
			salary_slip_ids: salary_slip_ids,
			action: action,
			reason: reason
		},
		callback: function(r) {
			if (r.message) {
				frappe.msgprint(r.message);
				report.refresh();
			}
		}
	});
}
